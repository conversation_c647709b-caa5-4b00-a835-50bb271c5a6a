<?php

namespace App\Console\Commands\Actions\Settings\GeneralSettings;

use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneUnitsMaster;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Route;

class ComplexUnitAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:complexUnitList {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get list of Complex Unit List';

    protected $formatterByKeys = ['id'];

    protected $mapper = [
        "id" => "unitmaster.unit_id",
        'unit_number' => 'unitmaster.unit_flat_number',
        'unit_category' => 'unitmaster.unit_category',
        'building' => 'unitmaster.soc_building_name',
        'soc_building_floor' => 'unitmaster.soc_building_floor'
    ];


    protected $hugeData = true;


    /**
     * Execute the console command.
     */
    public function apply()
    {
        $building = $this->input['building'] ?? $this->input['filters']['building'] ?? '';
        $buildingId = $this->input['building_id'] ?? $this->input['filters']['building_id'] ?? '';
        $buildingFloor = $this->input['soc_building_floor'] ?? $this->input['filters']['soc_building_floor'] ?? '';
        $unitCategory = $this->input['filters']['unit_category'] ?? '';
        $unitNumber = $this->input['filters']['unit_flat_number'] ?? '';
        $unitType = isset($this->input['filters']['unit_type']) ? explode(',', $this->input['filters']['unit_type']) : [];
        $occupancyType = isset($this->input['filters']['occupancy_type']) ? explode(',', $this->input['filters']['occupancy_type']) : [];
        $occupied_by = isset($this->input['filters']['occupied_by']) ? explode(',', $this->input['filters']['occupied_by']) : [];
        $searchTerm = $this->input['filters']['search'] ?? '';
        $company_id = $this->input['company_id'] ?? null;

        // Route identification
        $currentRoute = Route::current();
        $routeUri = $currentRoute->uri();

        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page'] : ($this->hugeData ? 100000 : 10);
        $offset = ($page - 1) * $per_page;
        $cancel_date = '0000-00-00';
        $current_date = $this->getCurrentDate('database');

        $obj = $this->tenantDB()->table('chsone_units_master as unitmaster')
            ->selectRaw(
                "unitmaster.unit_id AS id,
                unitmaster.unit_id,
                unitmaster.soc_id,
                unitmaster.unit_category,
                unitmaster.unit_type,
                unitmaster.soc_building_id,
                unitmaster.soc_building_name,
                unitmaster.soc_building_floor,
                CONCAT(unitmaster.soc_building_name, ', Floor: ', unitmaster.soc_building_floor) AS building,
                unitmaster.unit_flat_number,
                CONCAT(unitmaster.soc_building_name, '-', unitmaster.unit_flat_number) AS building_unit,
                CAST(unitmaster.unit_area AS DECIMAL(10,2)) AS unit_area,
                CAST(unitmaster.unit_open_area AS DECIMAL(10,2)) AS unit_open_area,
                unitmaster.effective_date,
                unitmaster.is_allotted,
                unitmaster.is_occupied,
                unitmaster.occupancy_type,
                unitmaster.occupied_by,
                unitmaster.vpa,
                unitmaster.status"
            )
            ->where('unitmaster.soc_id', $company_id)
            ->where('unitmaster.status', 1)
            ->where('cancel_date', '>', $current_date)
            ->orWhere('cancel_date', '=', $cancel_date);

        if ($building) {
            $obj->where('unitmaster.soc_building_name', 'like', '%' . $building . '%');
        }
        
        if ($buildingId) {
            $obj->where('unitmaster.soc_building_id', 'like', '%' . $buildingId . '%');
        }

        if ($buildingFloor) {
            $obj->where('unitmaster.soc_building_floor', 'like', '%' . $buildingFloor . '%');
        }

        if ($unitCategory) {
            $obj->where('unitmaster.unit_category', 'like', '%' . $unitCategory . '%');
        }

        if ($unitNumber) {
            $obj->where('unitmaster.unit_flat_number', 'like', '%' . $unitNumber . '%');
        }

        if ($unitType) {
            $obj->whereIn('unitmaster.unit_type', $unitType);
        }

        if ($occupancyType) {
            $obj->whereIn('unitmaster.occupancy_type', $occupancyType);
        }

        if ($occupied_by) {
            $obj->whereIn('unitmaster.occupied_by', $occupied_by);
        }

        if ($routeUri == 'api/v2/admin/units/list') {
            $obj->where('unitmaster.unit_type', 'flat');
        }
        
        $obj->orderBy('unitmaster.soc_building_name', 'asc')
            ->orderBy('unitmaster.soc_building_floor', 'asc');


        if (!empty($this->input['sort'])) {
            $sortBy = $this->input['sort'];
            $sortByKey = array_key_first($sortBy);
            $sortByValue = $sortBy[$sortByKey];
            $obj->orderBy($sortByKey, $sortByValue);
        }
        

        $columns = [
            'unitmaster.soc_building_name',
            'unitmaster.soc_building_floor',
            'unitmaster.unit_flat_number',
            'unitmaster.unit_category',
            'unitmaster.unit_type',
            'unitmaster.unit_area',
            'unitmaster.unit_open_area',
            'unitmaster.occupancy_type',
            'unitmaster.occupied_by',
            'unitmaster.vpa',
            'unitmaster.effective_date',
        ];

        if ($searchTerm) {
            $obj->where(function ($q) use ($columns, $searchTerm) {
                foreach ($columns as $column) {
                    $q->orWhere($column, 'LIKE', '%' . $searchTerm . '%');
                }
            });
        }

        $count = $obj->get()->count();

        $result = $obj->offset($offset)
            ->limit($per_page)
            ->get();

        $this->data = $result;
        $this->meta['pagination']['total'] = $count;
    }
}
